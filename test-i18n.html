<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多语言测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .language-switch {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        button.active {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Snapany Extension 多语言测试</h1>
    
    <div class="language-switch">
        <button id="btn-en" onclick="switchLanguage('en')">English</button>
        <button id="btn-zh" onclick="switchLanguage('zh_CN')">简体中文</button>
    </div>

    <div class="test-section">
        <h2>Chrome Extension 本地化测试</h2>
        <p>扩展名称: <span id="extension-name">__MSG_extensionName__</span></p>
        <p>扩展描述: <span id="extension-desc">__MSG_extensionDescription__</span></p>
    </div>

    <div class="test-section">
        <h2>React i18next 测试</h2>
        <div id="react-translations">
            <p>通用标题: <span data-key="common.title"></span></p>
            <p>加载中: <span data-key="common.loading"></span></p>
            <p>弹窗标题: <span data-key="popup.title"></span></p>
            <p>未找到媒体: <span data-key="popup.noMediaFound"></span></p>
            <p>播放视频提示: <span data-key="popup.playVideoToDetect"></span></p>
            <p>切换到侧边栏: <span data-key="toolbar.switchToSidepanel"></span></p>
            <p>清空当前标签页: <span data-key="toolbar.clearCurrentTab"></span></p>
        </div>
    </div>

    <script type="module">
        // 模拟翻译数据
        const translations = {
            en: {
                "common": {
                    "title": "Snapany Extension",
                    "loading": "Loading..."
                },
                "popup": {
                    "title": "Media Capture",
                    "noMediaFound": "No media found on this page",
                    "playVideoToDetect": "Click to play video to help detect files"
                },
                "toolbar": {
                    "switchToSidepanel": "Switch to Sidepanel",
                    "clearCurrentTab": "Clear current tab records"
                }
            },
            zh_CN: {
                "common": {
                    "title": "Snapany 扩展",
                    "loading": "加载中..."
                },
                "popup": {
                    "title": "媒体捕获",
                    "noMediaFound": "此页面未找到媒体内容",
                    "playVideoToDetect": "点击播放视频以帮助检测文件"
                },
                "toolbar": {
                    "switchToSidepanel": "切换到侧边栏模式",
                    "clearCurrentTab": "清空当前标签页的嗅探记录"
                }
            }
        };

        let currentLang = 'zh_CN';

        function getTranslation(key, lang = currentLang) {
            const keys = key.split('.');
            let result = translations[lang];
            
            for (const k of keys) {
                result = result?.[k];
                if (result === undefined) break;
            }
            
            return result || key;
        }

        function updateTranslations() {
            const elements = document.querySelectorAll('[data-key]');
            elements.forEach(el => {
                const key = el.getAttribute('data-key');
                el.textContent = getTranslation(key);
            });

            // 更新按钮状态
            document.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(currentLang === 'en' ? 'btn-en' : 'btn-zh').classList.add('active');
        }

        window.switchLanguage = function(lang) {
            currentLang = lang;
            updateTranslations();
        };

        // 初始化
        updateTranslations();
    </script>
</body>
</html>
