/**
 * 语言切换组件
 * 提供简单的语言切换功能
 */

import { useTranslation } from "react-i18next"

interface LanguageSwitcherProps {
  className?: string
}

export default function LanguageSwitcher({ className = "" }: LanguageSwitcherProps) {
  const { i18n, t } = useTranslation()

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'zh_CN', name: '简体中文' }
  ]

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode)
    // 可以在这里添加保存到本地存储的逻辑
    localStorage.setItem('snapany-language', languageCode)
  }

  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      <label className="text-sm font-medium text-gray-700">
        {t('settings.language')}
      </label>
      <select
        value={i18n.language}
        onChange={(e) => handleLanguageChange(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        {languages.map((lang) => (
          <option key={lang.code} value={lang.code}>
            {lang.name}
          </option>
        ))}
      </select>
    </div>
  )
}
