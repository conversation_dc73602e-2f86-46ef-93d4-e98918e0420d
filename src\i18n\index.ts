import i18n from 'i18next'\nimport { initReactI18next } from 'react-i18next'\n\nimport en from './locales/en.json'\nimport zh_CN from './locales/zh_CN.json'\n\n// 语言映射表\nconst languageMap: Record<string, any> = {\n  'en': en,\n  'zh': zh_CN,\n  'zh-CN': zh_CN,\n  'zh_CN': zh_CN\n}\n\n// 为 background script 提供的简单多语言函数\nexport function getBackgroundText(key: string): string {\n  const currentLanguage = chrome.i18n.getUILanguage()\n\n  // 尝试精确匹配\n  let translations = languageMap[currentLanguage]\n\n  // 如果没有精确匹配，尝试语言前缀匹配\n  if (!translations) {\n    const languagePrefix = currentLanguage.split('-')[0]\n    translations = languageMap[languagePrefix]\n  }\n\n  // 如果还是没有，回退到英语\n  if (!translations) {\n    translations = en\n  }\n\n  // 支持嵌套键，如 'common.title'\n  const keys = key.split('.')\n  let result: any = translations\n\n  for (const k of keys) {\n    result = result?.[k]\n    if (result === undefined) break\n  }\n\n  return result || key\n}\n\nconst resources = {\n  en: { translation: en },\n  zh_CN: { translation: zh_CN }\n}\n\n// 检测浏览器语言\nfunction detectLanguage(): string {\n  try {\n    const chromeLanguage = chrome?.i18n?.getUILanguage?.()\n    if (chromeLanguage) {\n      // 标准化语言代码\n      const normalizedLang = chromeLanguage.toLowerCase().replace('-', '_')\n\n      // 检查是否支持该语言\n      if (resources[normalizedLang]) {\n        return normalizedLang\n      }\n\n      // 尝试语言前缀匹配\n      const languagePrefix = normalizedLang.split('_')[0]\n      if (languagePrefix === 'zh') {\n        return 'zh_CN' // 默认使用简体中文\n      }\n      if (resources[languagePrefix]) {\n        return languagePrefix\n      }\n    }\n  } catch (error) {\n    console.log('Chrome i18n API不可用，使用navigator.language')\n  }\n\n  const browserLanguage = navigator.language || navigator.languages?.[0] || 'en'\n  const normalizedBrowserLang = browserLanguage.toLowerCase().replace('-', '_')\n\n  // 检查是否支持该语言\n  if (resources[normalizedBrowserLang]) {\n    return normalizedBrowserLang\n  }\n\n  // 尝试语言前缀匹配\n  const languagePrefix = normalizedBrowserLang.split('_')[0]\n  if (languagePrefix === 'zh') {\n    return 'zh_CN' // 默认使用简体中文\n  }\n  if (resources[languagePrefix]) {\n    return languagePrefix\n  }\n\n  // 回退到英语\n  return 'en'\n}\n\ni18n\n  .use(initReactI18next)\n  .init({\n    resources,\n    lng: detectLanguage(),\n    fallbackLng: 'en',\n    debug: process.env.NODE_ENV === 'development',\n\n    interpolation: {\n      escapeValue: false\n    }\n  })\n\nexport default i18n